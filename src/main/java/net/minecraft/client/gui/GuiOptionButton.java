package net.minecraft.client.gui;

import net.minecraft.client.settings.Options;

public class GuiO<PERSON><PERSON>utton extends Gui<PERSON>utton {
    private final Options.Settings enumSettings;

    public GuiOptionButton(int p_i45011_1_, int p_i45011_2_, int p_i45011_3_, String p_i45011_4_) {
        this(p_i45011_1_, p_i45011_2_, p_i45011_3_, null, p_i45011_4_);
    }

    public GuiOptionButton(int p_i45012_1_, int p_i45012_2_, int p_i45012_3_, int p_i45012_4_, int p_i45012_5_, String p_i45012_6_) {
        super(p_i45012_1_, p_i45012_2_, p_i45012_3_, p_i45012_4_, p_i45012_5_, p_i45012_6_);
        this.enumSettings = null;
    }

    public GuiOptionButton(int p_i45013_1_, int p_i45013_2_, int p_i45013_3_, Options.Settings p_i45013_4_, String p_i45013_5_) {
        super(p_i45013_1_, p_i45013_2_, p_i45013_3_, 150, 20, p_i45013_5_);
        this.enumSettings = p_i45013_4_;
    }

    public Options.Settings returnEnumOptions() {
        return this.enumSettings;
    }
}
