package net.minecraft.util;

import java.util.Random;
import java.util.UUID;

public class MathHelper {
    public static final float PI = roundToFloat(Math.PI);
    public static final float PI_HALF = roundToFloat(Math.PI / 2D);
    public static final float DEG_TO_RAD = roundToFloat(0.017453292519943295D);
    private static final float RAD_TO_INDEX = roundToFloat(651.8986469044033D);

    // Lookup tables for fast trigonometric calculations
    private static final float[] ASIN_TABLE = new float[65536];
    private static final float[] SIN_TABLE_FAST = new float[4096];
    private static final float[] SIN_TABLE = new float[65536];

    /**
     * Though it looks like an array, this is really more like a mapping.  Key (index of this array) is the upper 5 bits
     * of the result of multiplying a 32-bit unsigned integer by the B(2, 5) De Bruijn sequence 0x077CB531.  Value
     * (value stored in the array) is the unique index (from the right) of the leftmost one-bit in a 32-bit unsigned
     * integer that can cause the upper 5 bits to get that value.  Used for highly optimized "find the log-base-2 of
     * this number" calculations.
     */
    private static final int[] MULTIPLY_DE_BRUIJN_BIT_POSITION;
    private static final double ATAN2_MAGIC_NUMBER;
    private static final double[] ATAN2_TABLE_A;
    private static final double[] ATAN2_TABLE_B;
    public static boolean fastMath = false;

    static {
        // Initialize sine lookup tables
        for (int i = 0; i < 65536; ++i) {
            SIN_TABLE[i] = (float) Math.sin((double) i * Math.PI * 2.0D / 65536.0D);
        }

        for (int j = 0; j < SIN_TABLE_FAST.length; ++j) {
            SIN_TABLE_FAST[j] = roundToFloat(Math.sin((double) j * Math.PI * 2.0D / 4096.0D));
        }

        // Initialize asin lookup table (from MathUtils)
        for (int i = 0; i < 65536; ++i) {
            ASIN_TABLE[i] = (float) Math.asin((double) i / 32767.5D - 1.0D);
        }

        for (int j = -1; j < 2; ++j) {
            ASIN_TABLE[(int) (((double) j + 1.0D) * 32767.5D) & 65535] = (float) Math.asin(j);
        }

        MULTIPLY_DE_BRUIJN_BIT_POSITION = new int[]{0, 1, 28, 2, 29, 14, 24, 3, 30, 22, 20, 15, 25, 17, 4, 8, 31, 27, 13, 23, 21, 19, 16, 7, 26, 12, 18, 6, 11, 5, 10, 9};
        ATAN2_MAGIC_NUMBER = Double.longBitsToDouble(4805340802404319232L);
        ATAN2_TABLE_A = new double[257];
        ATAN2_TABLE_B = new double[257];

        for (int k = 0; k < 257; ++k) {
            double d0 = (double) k / 256.0D;
            double d1 = Math.asin(d0);
            ATAN2_TABLE_B[k] = Math.cos(d1);
            ATAN2_TABLE_A[k] = d1;
        }
    }

    // ========== TRIGONOMETRIC FUNCTIONS ==========

    /**
     * Fast sine calculation using lookup table
     */
    public static float sin(float angle) {
        return fastMath ? SIN_TABLE_FAST[(int) (angle * RAD_TO_INDEX) & 4095] : SIN_TABLE[(int) (angle * 10430.378F) & 65535];
    }

    /**
     * Fast cosine calculation using lookup table with appropriate offset
     */
    public static float cos(float angle) {
        return fastMath ? SIN_TABLE_FAST[(int) (angle * RAD_TO_INDEX + 1024.0F) & 4095] : SIN_TABLE[(int) (angle * 10430.378F + 16384.0F) & 65535];
    }

    /**
     * Fast arcsine calculation using lookup table (from MathUtils)
     */
    public static float asin(float value) {
        return ASIN_TABLE[(int) ((double) (value + 1.0F) * 32767.5D) & 65535];
    }

    /**
     * Fast arccosine calculation using lookup table (from MathUtils)
     */
    public static float acos(float value) {
        return (PI / 2F) - ASIN_TABLE[(int) ((double) (value + 1.0F) * 32767.5D) & 65535];
    }

    // ========== UTILITY FUNCTIONS ==========

    /**
     * Square root of a float value
     */
    public static float sqrt(float value) {
        return (float) Math.sqrt(value);
    }

    /**
     * Square root of a double value, returned as float
     */
    public static float sqrt(double value) {
        return (float) Math.sqrt(value);
    }

    /**
     * Converts degrees to radians (from MathUtils)
     */
    public static float toDegrees(float radians) {
        return radians * 180.0F / PI;
    }

    /**
     * Converts radians to degrees (from MathUtils)
     */
    public static float toRadians(float degrees) {
        return degrees / 180.0F * PI;
    }

    /**
     * Rounds a double to a float with high precision (from MathUtils)
     */
    public static float roundToFloat(double value) {
        return (float) ((double) Math.round(value * 1.0E8D) / 1.0E8D);
    }

    /**
     * Calculates the average of an integer array (from MathUtils)
     */
    public static int getAverage(int[] values) {
        if (values.length == 0) {
            return 0;
        } else {
            int sum = getSum(values);
            return sum / values.length;
        }
    }

    /**
     * Calculates the sum of an integer array (from MathUtils)
     */
    public static int getSum(int[] values) {
        if (values.length == 0) {
            return 0;
        } else {
            int sum = 0;
            for (int value : values) {
                sum += value;
            }
            return sum;
        }
    }

    // ========== FLOOR AND CEILING FUNCTIONS ==========

    /**
     * Returns the greatest integer less than or equal to the float argument
     */
    public static int floor(float value) {
        int i = (int) value;
        return value < (float) i ? i - 1 : i;
    }

    /**
     * Returns the greatest integer less than or equal to the double argument
     */
    public static int floor(double value) {
        int i = (int) value;
        return value < (double) i ? i - 1 : i;
    }

    /**
     * Long version of floor for double values
     */
    public static long floorLong(double value) {
        long i = (long) value;
        return value < (double) i ? i - 1L : i;
    }

    /**
     * Returns the smallest integer greater than or equal to the float argument
     */
    public static int ceil(float value) {
        int i = (int) value;
        return value > (float) i ? i + 1 : i;
    }

    /**
     * Returns the smallest integer greater than or equal to the double argument
     */
    public static int ceil(double value) {
        int i = (int) value;
        return value > (double) i ? i + 1 : i;
    }

    /**
     * Truncates a double to an int, clamped to prevent overflow
     */
    public static int truncateDoubleToInt(double value) {
        return (int) (value + 1024.0D) - 1024;
    }

    // ========== ABSOLUTE VALUE AND CLAMPING ==========

    /**
     * Returns the absolute value of a float
     */
    public static float abs(float value) {
        return Math.abs(value);
    }

    /**
     * Returns the absolute value of an int
     */
    public static int abs(int value) {
        return Math.abs(value);
    }

    /**
     * Returns the value clamped to be within the specified range
     */
    public static int clamp(int value, int min, int max) {
        return Math.clamp(value, min, max);
    }

    /**
     * Returns the value clamped to be within the specified range
     */
    public static float clamp(float value, float min, float max) {
        return Math.clamp(value, min, max);
    }

    /**
     * Returns the value clamped to be within the specified range
     */
    public static double clamp(double value, double min, double max) {
        return Math.clamp(value, min, max);
    }

    // ========== INTERPOLATION AND COMPARISON ==========

    /**
     * Linear interpolation between two values based on a slide parameter
     */
    public static double lerp(double min, double max, double slide) {
        return slide < 0.0D ? min : (slide > 1.0D ? max : min + (max - min) * slide);
    }

    public static float lerp(float min, float max, float slide) {
        return Math.max(min, Math.min(max, min + (max - min) * slide));
    }

    /**
     * Returns the maximum of the absolute values of two numbers
     */
    public static double absMax(double a, double b) {
        return Math.max(Math.abs(a), Math.abs(b));
    }

    /**
     * Checks if two floats are approximately equal within epsilon tolerance
     */
    public static boolean epsilonEquals(float a, float b) {
        return abs(b - a) < 1.0E-5F;
    }

    // ========== RANDOM NUMBER UTILITIES ==========

    /**
     * Returns a random integer within the specified range (inclusive)
     */
    public static int randomIntegerInRange(Random random, int min, int max) {
        return min >= max ? min : random.nextInt(max - min + 1) + min;
    }

    /**
     * Returns a random float within the specified range
     */
    public static float randomFloatInRange(Random random, float min, float max) {
        return min >= max ? min : random.nextFloat() * (max - min) + min;
    }

    /**
     * Returns a random double within the specified range
     */
    public static double randomDoubleInRange(Random random, double min, double max) {
        return min >= max ? min : random.nextDouble() * (max - min) + min;
    }

    /**
     * Calculates the average of a long array
     */
    public static double average(long[] values) {
        long sum = 0L;
        for (long value : values) {
            sum += value;
        }
        return (double) sum / (double) values.length;
    }

    // ========== MATHEMATICAL UTILITIES ==========

    /**
     * Buckets an integer with specified bucket sizes
     */
    public static int bucketInt(int value, int bucketSize) {
        return value < 0 ? -((-value - 1) / bucketSize) - 1 : value / bucketSize;
    }

    // ========== ANGLE UTILITIES ==========

    /**
     * Normalizes an angle to be within a specified range
     */
    public static int normalizeAngle(int angle, int range) {
        return (angle % range + range) % range;
    }

    /**
     * Wraps an angle to be between -180 and +180 degrees
     */
    public static float wrapDegrees(float angle) {
        angle = angle % 360.0F;

        if (angle >= 180.0F) angle -= 360.0F;
        if (angle < -180.0F) angle += 360.0F;

        return angle;
    }

    /**
     * Wraps an angle to be between -180 and +180 degrees
     */
    public static double wrapDegrees(double angle) {
        angle = angle % 360.0D;

        if (angle >= 180.0D) angle -= 360.0D;

        if (angle < -180.0D) angle += 360.0D;

        return angle;
    }

    // ========== PARSING UTILITIES ==========

    /**
     * Parses a string as an integer, returning a default value if parsing fails
     */
    public static int parseIntWithDefault(String str, int defaultValue) {
        try {
            return Integer.parseInt(str);
        } catch (Throwable e) {
            return defaultValue;
        }
    }

    /**
     * Parses a string as an integer with a default value, then clamps to a minimum
     */
    public static int parseIntWithDefaultAndMin(String str, int defaultValue, int minValue) {
        return Math.max(minValue, parseIntWithDefault(str, defaultValue));
    }

    /**
     * Parses a string as a double, returning a default value if parsing fails
     */
    public static double parseDoubleWithDefault(String str, double defaultValue) {
        try {
            return Double.parseDouble(str);
        } catch (Throwable e) {
            return defaultValue;
        }
    }

    /**
     * Parses a string as a double with a default value, then clamps to a minimum
     */
    public static double parseDoubleWithDefaultAndMin(String str, double defaultValue, double minValue) {
        return Math.max(minValue, parseDoubleWithDefault(str, defaultValue));
    }

    // ========== BIT MANIPULATION ==========

    /**
     * Returns the input value rounded up to the next highest power of two
     */
    public static int roundUpToPowerOfTwo(int value) {
        int i = value - 1;
        i = i | i >> 1;
        i = i | i >> 2;
        i = i | i >> 4;
        i = i | i >> 8;
        i = i | i >> 16;
        return i + 1;
    }

    /**
     * Checks if the given value is a power of two (1, 2, 4, 8, 16, ...)
     */
    public static boolean isPowerOfTwo(int value) {
        return value != 0 && (value & value - 1) == 0;
    }

    /**
     * Uses a De Bruijn sequence and lookup table to efficiently calculate log base 2
     */
    private static int calculateLogBaseTwoDeBruijn(int value) {
        value = isPowerOfTwo(value) ? value : roundUpToPowerOfTwo(value);
        return MULTIPLY_DE_BRUIJN_BIT_POSITION[(int) ((long) value * 125613361L >> 27) & 31];
    }

    /**
     * Efficiently calculates the floor of the base-2 log of an integer value
     * This is effectively the index of the highest bit that is set
     */
    public static int calculateLogBaseTwo(int value) {
        return calculateLogBaseTwoDeBruijn(value) - (isPowerOfTwo(value) ? 0 : 1);
    }

    /**
     * Rounds a value up to the nearest multiple of another value
     */
    public static int roundUp(int value, int multiple) {
        if (multiple == 0) {
            return 0;
        } else if (value == 0) {
            return multiple;
        } else {
            if (value < 0) {
                multiple *= -1;
            }

            int remainder = value % multiple;
            return remainder == 0 ? value : value + multiple - remainder;
        }
    }

    // ========== COLOR UTILITIES ==========

    /**
     * Packs RGB float values (0.0-1.0) into a single integer
     */
    public static int packRGB(float red, float green, float blue) {
        return packRGB(floor(red * 255.0F), floor(green * 255.0F), floor(blue * 255.0F));
    }

    /**
     * Packs RGB integer values (0-255) into a single integer
     */
    public static int packRGB(int red, int green, int blue) {
        int packed = (red << 8) + green;
        packed = (packed << 8) + blue;
        return packed;
    }

    /**
     * Multiplies two packed RGB colors component-wise
     */
    public static int multiplyColors(int color1, int color2) {
        int r1 = (color1 & 16711680) >> 16;
        int r2 = (color2 & 16711680) >> 16;
        int g1 = (color1 & 65280) >> 8;
        int g2 = (color2 & 65280) >> 8;
        int b1 = (color1 & 255);
        int b2 = (color2 & 255);
        int rResult = (int) ((float) r1 * (float) r2 / 255.0F);
        int gResult = (int) ((float) g1 * (float) g2 / 255.0F);
        int bResult = (int) ((float) b1 * (float) b2 / 255.0F);
        return color1 & -16777216 | rResult << 16 | gResult << 8 | bResult;
    }

    /**
     * Returns the fractional part of a double value
     */
    public static double fractionalPart(double value) {
        return value - Math.floor(value);
    }

    // ========== COORDINATE AND UUID UTILITIES ==========

    /**
     * Generates a pseudo-random number based on a position
     */
    public static long getPositionRandom(Vec3i pos) {
        return getCoordinateRandom(pos.getX(), pos.getY(), pos.getZ());
    }

    /**
     * Generates a pseudo-random number based on coordinates
     */
    public static long getCoordinateRandom(int x, int y, int z) {
        long hash = (x * 3129871L) ^ (long) z * 116129781L ^ (long) y;
        hash = hash * hash * 42317861L + hash * 11L;
        return hash;
    }

    /**
     * Generates a random UUID using the provided Random instance
     */
    public static UUID getRandomUuid(Random rand) {
        long mostSigBits = rand.nextLong() & -61441L | 16384L;
        long leastSigBits = rand.nextLong() & 4611686018427387903L | Long.MIN_VALUE;
        return new UUID(mostSigBits, leastSigBits);
    }

    /**
     * Calculates the inverse linear interpolation parameter
     */
    public static double inverseLerp(double value, double min, double max) {
        return (value - min) / (max - min);
    }

    // ========== ADVANCED MATHEMATICAL FUNCTIONS ==========

    /**
     * Fast atan2 implementation using lookup tables
     */
    public static double atan2(double y, double x) {
        double distanceSquared = x * x + y * y;

        if (Double.isNaN(distanceSquared)) {
            return Double.NaN;
        } else {
            boolean yNegative = y < 0.0D;

            if (yNegative) {
                y = -y;
            }

            boolean xNegative = x < 0.0D;

            if (xNegative) {
                x = -x;
            }

            boolean swapped = y > x;

            if (swapped) {
                double temp = x;
                x = y;
                y = temp;
            }

            double invSqrt = fastInverseSqrt(distanceSquared);
            x = x * invSqrt;
            y = y * invSqrt;
            double d2 = ATAN2_MAGIC_NUMBER + y;
            int i = (int) Double.doubleToRawLongBits(d2);
            double d3 = ATAN2_TABLE_A[i];
            double d4 = ATAN2_TABLE_B[i];
            double d5 = d2 - ATAN2_MAGIC_NUMBER;
            double d6 = y * d4 - x * d5;
            double d7 = (6.0D + d6 * d6) * d6 * 0.16666666666666666D;
            double result = d3 + d7;

            if (swapped) {
                result = (Math.PI / 2D) - result;
            }

            if (xNegative) {
                result = Math.PI - result;
            }

            if (yNegative) {
                result = -result;
            }

            return result;
        }
    }

    /**
     * Fast inverse square root using bit manipulation (Quake algorithm)
     */
    private static double fastInverseSqrt(double value) {
        double half = 0.5D * value;
        long bits = Double.doubleToRawLongBits(value);
        bits = 6910469410427058090L - (bits >> 1);
        value = Double.longBitsToDouble(bits);
        value = value * (1.5D - half * value * value);
        return value;
    }

    /**
     * Converts HSV color values to RGB color as a packed integer
     *
     * @param hue        Hue component (0.0-1.0)
     * @param saturation Saturation component (0.0-1.0)
     * @param value      Value/brightness component (0.0-1.0)
     * @return RGB color as packed integer
     */
    public static int hsvToRGB(float hue, float saturation, float value) {
        int sector = (int) (hue * 6.0F) % 6;
        float fractional = hue * 6.0F - (float) sector;
        float p = value * (1.0F - saturation);
        float q = value * (1.0F - fractional * saturation);
        float t = value * (1.0F - (1.0F - fractional) * saturation);
        float red, green, blue;

        switch (sector) {
            case 0 -> {
                red = value;
                green = t;
                blue = p;
            }
            case 1 -> {
                red = q;
                green = value;
                blue = p;
            }
            case 2 -> {
                red = p;
                green = value;
                blue = t;
            }
            case 3 -> {
                red = p;
                green = q;
                blue = value;
            }
            case 4 -> {
                red = t;
                green = p;
                blue = value;
            }
            case 5 -> {
                red = value;
                green = p;
                blue = q;
            }
            default ->
                    throw new RuntimeException("Invalid HSV to RGB conversion. Input: " + hue + ", " + saturation + ", " + value);
        }

        int r = clamp((int) (red * 255.0F), 0, 255);
        int g = clamp((int) (green * 255.0F), 0, 255);
        int b = clamp((int) (blue * 255.0F), 0, 255);
        return r << 16 | g << 8 | b;
    }
}
