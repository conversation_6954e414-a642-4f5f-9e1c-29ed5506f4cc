package net.minecraft.client.shader;

import org.lwjgl.util.vector.Matrix4f;

public class ShaderDefault extends ShaderUniform {
    public ShaderDefault() {
        super("dummy", 4, 1, null);
    }

    public void set(float p_148090_1_) {
    }

    public void set(float p_148087_1_, float p_148087_2_) {
    }

    public void set(float p_148095_1_, float p_148095_2_, float p_148095_3_) {
    }

    public void set(float p_148081_1_, float p_148081_2_, float p_148081_3_, float p_148081_4_) {
    }

    public void func_148092_b(float p_148092_1_, float p_148092_2_, float p_148092_3_, float p_148092_4_) {
    }

    public void set(int p_148083_1_, int p_148083_2_, int p_148083_3_, int p_148083_4_) {
    }

    public void set(float[] p_148097_1_) {
    }

    public void set(float p_148094_1_, float p_148094_2_, float p_148094_3_, float p_148094_4_, float p_148094_5_, float p_148094_6_, float p_148094_7_, float p_148094_8_, float p_148094_9_, float p_148094_10_, float p_148094_11_, float p_148094_12_, float p_148094_13_, float p_148094_14_, float p_148094_15_, float p_148094_16_) {
    }

    public void set(Matrix4f p_148088_1_) {
    }
}
