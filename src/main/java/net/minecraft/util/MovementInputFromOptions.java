package net.minecraft.util;

import net.minecraft.client.settings.Options;
import oryx.event.impl.MoveInputEvent;

public class MovementInputFromOptions extends MovementInput {
    private final Options options;

    public MovementInputFromOptions(Options optionsIn) {
        this.options = optionsIn;
    }

    public void updatePlayerMoveState() {
        this.moveStrafe = 0.0F;
        this.moveForward = 0.0F;

        if (this.options.keyBindForward.isKeyDown()) {
            ++this.moveForward;
        }

        if (this.options.keyBindBack.isKeyDown()) {
            --this.moveForward;
        }

        if (this.options.keyBindLeft.isKeyDown()) {
            ++this.moveStrafe;
        }

        if (this.options.keyBindRight.isKeyDown()) {
            --this.moveStrafe;
        }

        this.jump = this.options.keyBindJump.isKeyDown();
        this.sneak = this.options.keyBindSneak.isKeyDown();

        var moveInputEvent = new MoveInputEvent(moveForward, moveStrafe, jump, sneak, 0.3D);
        moveInputEvent.call();

        final double sneakMultiplier = moveInputEvent.getSneakSlowDown();
        this.moveForward = moveInputEvent.getForward();
        this.moveStrafe = moveInputEvent.getStrafe();
        this.jump = moveInputEvent.getJump();
        this.sneak = moveInputEvent.getSneak();

        if (this.sneak) {
            this.moveStrafe = (float) ((double) this.moveStrafe * sneakMultiplier);
            this.moveForward = (float) ((double) this.moveForward * sneakMultiplier);
        }
    }
}
