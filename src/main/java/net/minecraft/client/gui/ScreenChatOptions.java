package net.minecraft.client.gui;

import net.minecraft.client.resources.I18n;
import net.minecraft.client.settings.Options;

import java.io.IOException;

public class ScreenChatOptions extends GuiScreen {
    private static final Options.Settings[] field_146399_a = new Options.Settings[]{Options.Settings.CHAT_VISIBILITY, Options.Settings.CHAT_COLOR, Options.Settings.CHAT_LINKS, Options.Settings.CHAT_OPACITY, Options.Settings.CHAT_LINKS_PROMPT, Options.Settings.CHAT_SCALE, Options.Settings.CHAT_HEIGHT_FOCUSED, Options.Settings.CHAT_HEIGHT_UNFOCUSED, Options.Settings.CHAT_WIDTH, Options.Settings.REDUCED_DEBUG_INFO};
    private final GuiScreen parentScreen;
    private final Options game_settings;
    private String field_146401_i;

    public ScreenChatOptions(GuiScreen parentScreenIn, Options optionsIn) {
        this.parentScreen = parentScreenIn;
        this.game_settings = optionsIn;
    }

    /**
     * Adds the buttons (and other controls) to the screen in question. Called when the GUI is displayed and when the
     * window resizes, the buttonList is cleared beforehand.
     */
    public void initGui() {
        int i = 0;
        this.field_146401_i = I18n.format("options.chat.title");

        for (Options.Settings gamesettings$settings : field_146399_a) {
            if (gamesettings$settings.getEnumFloat()) {
                this.buttonList.add(new GuiOptionSlider(gamesettings$settings.returnEnumOrdinal(), this.width / 2 - 155 + i % 2 * 160, this.height / 6 + 24 * (i >> 1), gamesettings$settings));
            } else {
                this.buttonList.add(new GuiOptionButton(gamesettings$settings.returnEnumOrdinal(), this.width / 2 - 155 + i % 2 * 160, this.height / 6 + 24 * (i >> 1), gamesettings$settings, this.game_settings.getKeyBinding(gamesettings$settings)));
            }

            ++i;
        }

        this.buttonList.add(new GuiButton(200, this.width / 2 - 100, this.height / 6 + 120, I18n.format("gui.done")));
    }

    /**
     * Called by the controls from the buttonList when activated. (Mouse pressed for buttons)
     */
    protected void actionPerformed(GuiButton button) throws IOException {
        if (button.enabled) {
            if (button.id < 100 && button instanceof GuiOptionButton) {
                this.game_settings.setOptionValue(((GuiOptionButton) button).returnEnumOptions(), 1);
                button.displayString = this.game_settings.getKeyBinding(Options.Settings.getEnumOptions(button.id));
            }

            if (button.id == 200) {
                this.mc.options.saveOptions();
                this.mc.displayGuiScreen(this.parentScreen);
            }
        }
    }

    /**
     * Draws the screen and all the components in it. Args : mouseX, mouseY, renderPartialTicks
     */
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        this.drawDefaultBackground();
        this.drawCenteredString(this.fontRendererObj, this.field_146401_i, this.width / 2, 20, 16777215);
        super.drawScreen(mouseX, mouseY, partialTicks);
    }
}
