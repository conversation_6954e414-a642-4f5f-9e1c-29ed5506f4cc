package net.minecraft.util;

public class MouseFilter {
    private float mouse;
    private float smooth;
    private float lastDelta;

    /**
     * Smooths mouse input
     */
    public float smooth(float delta, float smoothFactor) {
        mouse += delta;
        delta = (mouse - smooth) * smoothFactor;
        //delta = MathHelper.lerp(mouse, smooth, smoothFactor);

        lastDelta += (delta - lastDelta) * 0.5F;
        //lastDelta += MathHelper.lerp(lastDelta, delta, 0.5F);

        if (delta > 0.0F && delta > lastDelta || delta < 0.0F && delta < lastDelta) {
            delta = lastDelta;
        }

        smooth += delta;
        return delta;
    }

    public void reset() {
        mouse = 0.0F;
        smooth = 0.0F;
        lastDelta = 0.0F;
    }
}
